-- Configuration file for Discord Bridge addon
DiscordBridge = DiscordBridge or {}
DiscordBridge.Config = {}

-- Default configuration
DiscordBridge.Config.defaults = {
    -- API endpoint for sending messages to Discord
    apiEndpoint = "http://localhost:3000/api/message",
    
    -- Your character name (will be auto-detected)
    characterName = "",
    
    -- Server/realm name (will be auto-detected)
    realmName = "",
    
    -- Enable/disable the addon
    enabled = true,
    
    -- Debug mode
    debug = false,
    
    -- Retry settings for failed API calls
    maxRetries = 3,
    retryDelay = 2, -- seconds
    
    -- Rate limiting
    messageQueueSize = 100,
    sendInterval = 0.5, -- seconds between API calls
}

-- Initialize saved variables
function DiscordBridge.Config:Initialize()
    if not DiscordBridgeDB then
        DiscordBridgeDB = {}
    end
    
    -- Merge defaults with saved settings
    for key, value in pairs(self.defaults) do
        if DiscordBridgeDB[key] == nil then
            DiscordBridgeDB[key] = value
        end
    end
    
    -- Auto-detect character and realm
    if DiscordBridgeDB.characterName == "" then
        DiscordBridgeDB.characterName = UnitName("player") or ""
    end
    
    if DiscordBridgeDB.realmName == "" then
        DiscordBridgeDB.realmName = GetRealmName() or ""
    end
end

-- Get configuration value
function DiscordBridge.Config:Get(key)
    return DiscordBridgeDB[key]
end

-- Set configuration value
function DiscordBridge.Config:Set(key, value)
    DiscordBridgeDB[key] = value
end
