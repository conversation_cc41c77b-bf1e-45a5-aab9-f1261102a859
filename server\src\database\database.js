const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const Logger = require('../utils/logger');

class Database {
    constructor() {
        this.dbPath = process.env.DATABASE_PATH || './data/conversations.db';
        this.db = null;
        
        // Ensure data directory exists
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
    }
    
    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    Logger.error('Failed to connect to database:', err);
                    reject(err);
                    return;
                }
                
                Logger.info('Connected to SQLite database');
                this.createTables().then(resolve).catch(reject);
            });
        });
    }
    
    async createTables() {
        const tables = [
            // Conversations table
            `CREATE TABLE IF NOT EXISTS conversations (
                id TEXT PRIMARY KEY,
                participant1_name TEXT NOT NULL,
                participant1_realm TEXT NOT NULL,
                participant1_character TEXT NOT NULL,
                participant2_name TEXT NOT NULL,
                participant2_realm TEXT NOT NULL,
                participant2_character TEXT NOT NULL,
                discord_channel_id TEXT UNIQUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )`,
            
            // Messages table
            `CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                conversation_id TEXT NOT NULL,
                sender_name TEXT NOT NULL,
                sender_realm TEXT NOT NULL,
                sender_character TEXT NOT NULL,
                recipient_name TEXT NOT NULL,
                recipient_realm TEXT NOT NULL,
                recipient_character TEXT NOT NULL,
                message_content TEXT NOT NULL,
                message_type TEXT DEFAULT 'whisper',
                timestamp DATETIME NOT NULL,
                discord_message_id TEXT,
                wow_timestamp REAL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (conversation_id) REFERENCES conversations (id)
            )`,
            
            // Characters table (to track all your characters)
            `CREATE TABLE IF NOT EXISTS characters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                realm TEXT NOT NULL,
                is_main BOOLEAN DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(name, realm)
            )`,
            
            // Discord channels table
            `CREATE TABLE IF NOT EXISTS discord_channels (
                id TEXT PRIMARY KEY,
                conversation_id TEXT NOT NULL,
                channel_name TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (conversation_id) REFERENCES conversations (id)
            )`
        ];
        
        for (const tableSQL of tables) {
            await this.run(tableSQL);
        }
        
        // Create indexes
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_conversations_participants ON conversations (participant1_name, participant2_name)',
            'CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages (conversation_id)',
            'CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_characters_name_realm ON characters (name, realm)'
        ];
        
        for (const indexSQL of indexes) {
            await this.run(indexSQL);
        }
        
        Logger.info('Database tables created/verified');
    }
    
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    Logger.error('Database run error:', { sql, params, error: err.message });
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }
    
    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    Logger.error('Database get error:', { sql, params, error: err.message });
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }
    
    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    Logger.error('Database all error:', { sql, params, error: err.message });
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }
    
    // Conversation methods
    async createConversation(conversationData) {
        const sql = `INSERT INTO conversations 
            (id, participant1_name, participant1_realm, participant1_character,
             participant2_name, participant2_realm, participant2_character,
             discord_channel_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
        
        const params = [
            conversationData.id,
            conversationData.participant1_name,
            conversationData.participant1_realm,
            conversationData.participant1_character,
            conversationData.participant2_name,
            conversationData.participant2_realm,
            conversationData.participant2_character,
            conversationData.discord_channel_id
        ];
        
        return await this.run(sql, params);
    }
    
    async getConversation(conversationId) {
        const sql = 'SELECT * FROM conversations WHERE id = ?';
        return await this.get(sql, [conversationId]);
    }
    
    async getConversationByChannelId(channelId) {
        const sql = 'SELECT * FROM conversations WHERE discord_channel_id = ?';
        return await this.get(sql, [channelId]);
    }
    
    async updateConversationActivity(conversationId) {
        const sql = 'UPDATE conversations SET last_activity = CURRENT_TIMESTAMP WHERE id = ?';
        return await this.run(sql, [conversationId]);
    }
    
    // Message methods
    async saveMessage(messageData) {
        const sql = `INSERT INTO messages 
            (conversation_id, sender_name, sender_realm, sender_character,
             recipient_name, recipient_realm, recipient_character,
             message_content, message_type, timestamp, discord_message_id, wow_timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        
        const params = [
            messageData.conversation_id,
            messageData.sender_name,
            messageData.sender_realm,
            messageData.sender_character,
            messageData.recipient_name,
            messageData.recipient_realm,
            messageData.recipient_character,
            messageData.message_content,
            messageData.message_type || 'whisper',
            messageData.timestamp,
            messageData.discord_message_id || null,
            messageData.wow_timestamp || null
        ];
        
        return await this.run(sql, params);
    }
    
    async getConversationMessages(conversationId, limit = 50) {
        const sql = `SELECT * FROM messages 
                     WHERE conversation_id = ? 
                     ORDER BY timestamp DESC 
                     LIMIT ?`;
        return await this.all(sql, [conversationId, limit]);
    }
    
    // Character methods
    async addCharacter(name, realm, isMain = false) {
        const sql = `INSERT OR REPLACE INTO characters (name, realm, is_main, is_active) 
                     VALUES (?, ?, ?, 1)`;
        return await this.run(sql, [name, realm, isMain]);
    }
    
    async getCharacters() {
        const sql = 'SELECT * FROM characters WHERE is_active = 1 ORDER BY is_main DESC, name';
        return await this.all(sql);
    }
    
    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        Logger.error('Error closing database:', err);
                    } else {
                        Logger.info('Database connection closed');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = Database;
