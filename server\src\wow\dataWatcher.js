const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const Logger = require('../utils/logger');

class WoWDataWatcher {
    constructor(conversationManager) {
        this.conversationManager = conversationManager;
        this.watcher = null;
        this.isWatching = false;
        
        // Configuration
        this.wowDataPath = process.env.WOW_ADDON_DATA_PATH || '../WTF/Account/*/SavedVariables/DiscordBridge.lua';
        this.pollInterval = 5000; // 5 seconds
        this.lastProcessedData = new Map();
    }
    
    async initialize() {
        try {
            // Try to find WoW SavedVariables files
            const savedVarsPaths = this.findSavedVariablesFiles();
            
            if (savedVarsPaths.length === 0) {
                Logger.warn('No WoW SavedVariables files found. Will use polling method.');
                this.startPolling();
            } else {
                Logger.info(`Found ${savedVarsPaths.length} SavedVariables files to watch`);
                this.startFileWatching(savedVarsPaths);
            }
            
            this.isWatching = true;
            Logger.info('WoW data watcher initialized');
            
        } catch (error) {
            Logger.error('Failed to initialize WoW data watcher:', error);
            throw error;
        }
    }
    
    findSavedVariablesFiles() {
        const paths = [];
        
        try {
            // Look for WoW installation directories
            const possiblePaths = [
                '../WTF/Account',
                '../../WTF/Account',
                '../../../WTF/Account',
                process.env.WOW_ADDON_DATA_PATH
            ].filter(Boolean);
            
            for (const basePath of possiblePaths) {
                if (fs.existsSync(basePath)) {
                    // Find all account directories
                    const accountDirs = fs.readdirSync(basePath, { withFileTypes: true })
                        .filter(dirent => dirent.isDirectory())
                        .map(dirent => dirent.name);
                    
                    for (const accountDir of accountDirs) {
                        const savedVarsPath = path.join(basePath, accountDir, 'SavedVariables', 'DiscordBridge.lua');
                        if (fs.existsSync(savedVarsPath)) {
                            paths.push(savedVarsPath);
                            Logger.info(`Found SavedVariables file: ${savedVarsPath}`);
                        }
                    }
                }
            }
        } catch (error) {
            Logger.warn('Error searching for SavedVariables files:', error);
        }
        
        return paths;
    }
    
    startFileWatching(filePaths) {
        try {
            this.watcher = chokidar.watch(filePaths, {
                ignored: /^\./, // ignore dotfiles
                persistent: true,
                usePolling: true, // Use polling for better compatibility
                interval: 2000 // Poll every 2 seconds
            });
            
            this.watcher
                .on('change', (filePath) => {
                    Logger.debug(`SavedVariables file changed: ${filePath}`);
                    this.processSavedVariablesFile(filePath);
                })
                .on('error', (error) => {
                    Logger.error('File watcher error:', error);
                });
            
            // Process existing files on startup
            for (const filePath of filePaths) {
                this.processSavedVariablesFile(filePath);
            }
            
            Logger.info('File watching started');
            
        } catch (error) {
            Logger.error('Failed to start file watching:', error);
            this.startPolling(); // Fallback to polling
        }
    }
    
    startPolling() {
        // Fallback method: poll for data periodically
        this.pollTimer = setInterval(() => {
            this.pollForData();
        }, this.pollInterval);
        
        Logger.info('Started polling for WoW data');
    }
    
    async processSavedVariablesFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const data = this.parseLuaFile(content);
            
            if (data && data.pendingMessages) {
                await this.processPendingMessages(data.pendingMessages, filePath);
            }
            
        } catch (error) {
            Logger.error(`Error processing SavedVariables file ${filePath}:`, error);
        }
    }
    
    parseLuaFile(content) {
        try {
            // Simple Lua parser for DiscordBridge SavedVariables
            // This is a basic implementation - you might want to use a proper Lua parser
            
            const data = {};
            
            // Extract DiscordBridgeDB table
            const dbMatch = content.match(/DiscordBridgeDB\s*=\s*{([\s\S]*?)^}/m);
            if (!dbMatch) {
                return null;
            }
            
            const dbContent = dbMatch[1];
            
            // Extract pendingMessages array
            const messagesMatch = dbContent.match(/\["pendingMessages"\]\s*=\s*{([\s\S]*?)}/);
            if (messagesMatch) {
                data.pendingMessages = this.parseLuaArray(messagesMatch[1]);
            }
            
            return data;
            
        } catch (error) {
            Logger.error('Error parsing Lua file:', error);
            return null;
        }
    }
    
    parseLuaArray(arrayContent) {
        const messages = [];
        
        try {
            // Split by array elements (simple approach)
            const elements = arrayContent.split(/\s*{\s*/).slice(1);
            
            for (const element of elements) {
                const endIndex = element.indexOf('}');
                if (endIndex === -1) continue;
                
                const elementContent = element.substring(0, endIndex);
                const message = this.parseLuaTable(elementContent);
                
                if (message) {
                    messages.push(message);
                }
            }
        } catch (error) {
            Logger.error('Error parsing Lua array:', error);
        }
        
        return messages;
    }
    
    parseLuaTable(tableContent) {
        const obj = {};
        
        try {
            // Extract key-value pairs
            const pairs = tableContent.match(/\["([^"]+)"\]\s*=\s*"([^"]*)"|\["([^"]+)"\]\s*=\s*([^,\s]+)/g);
            
            if (pairs) {
                for (const pair of pairs) {
                    const stringMatch = pair.match(/\["([^"]+)"\]\s*=\s*"([^"]*)"/);
                    const numberMatch = pair.match(/\["([^"]+)"\]\s*=\s*([^,\s]+)/);
                    
                    if (stringMatch) {
                        obj[stringMatch[1]] = stringMatch[2];
                    } else if (numberMatch) {
                        const value = numberMatch[2];
                        obj[numberMatch[1]] = isNaN(value) ? value : parseFloat(value);
                    }
                }
            }
        } catch (error) {
            Logger.error('Error parsing Lua table:', error);
        }
        
        return Object.keys(obj).length > 0 ? obj : null;
    }
    
    async processPendingMessages(messages, filePath) {
        if (!Array.isArray(messages) || messages.length === 0) {
            return;
        }
        
        Logger.info(`Processing ${messages.length} pending messages from ${filePath}`);
        
        for (const messageData of messages) {
            try {
                if (messageData.data) {
                    // Parse the JSON data from the message
                    const parsedData = JSON.parse(messageData.data);
                    await this.conversationManager.handleWoWMessage(parsedData);
                }
            } catch (error) {
                Logger.error('Error processing pending message:', error);
            }
        }
        
        // Clear processed messages from the file
        await this.clearProcessedMessages(filePath);
    }
    
    async clearProcessedMessages(filePath) {
        try {
            // Read the current file
            const content = fs.readFileSync(filePath, 'utf8');
            
            // Replace pendingMessages with empty array
            const updatedContent = content.replace(
                /(\["pendingMessages"\]\s*=\s*){[^}]*}/,
                '$1{}'
            );
            
            // Write back to file
            fs.writeFileSync(filePath, updatedContent, 'utf8');
            
            Logger.debug(`Cleared processed messages from ${filePath}`);
            
        } catch (error) {
            Logger.error(`Error clearing processed messages from ${filePath}:`, error);
        }
    }
    
    async pollForData() {
        // Alternative method: check for data files in a specific directory
        try {
            const messageDir = path.join(__dirname, '../../messages');
            
            if (!fs.existsSync(messageDir)) {
                return;
            }
            
            const files = fs.readdirSync(messageDir);
            const jsonFiles = files.filter(file => file.endsWith('.json'));
            
            for (const file of jsonFiles) {
                const filePath = path.join(messageDir, file);
                
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const messageData = JSON.parse(content);
                    
                    await this.conversationManager.handleWoWMessage(messageData);
                    
                    // Delete processed file
                    fs.unlinkSync(filePath);
                    
                    Logger.debug(`Processed message file: ${file}`);
                    
                } catch (error) {
                    Logger.error(`Error processing message file ${file}:`, error);
                }
            }
            
        } catch (error) {
            Logger.error('Error during polling:', error);
        }
    }
    
    async stop() {
        this.isWatching = false;
        
        if (this.watcher) {
            await this.watcher.close();
            this.watcher = null;
        }
        
        if (this.pollTimer) {
            clearInterval(this.pollTimer);
            this.pollTimer = null;
        }
        
        Logger.info('WoW data watcher stopped');
    }
}

module.exports = WoWDataWatcher;
