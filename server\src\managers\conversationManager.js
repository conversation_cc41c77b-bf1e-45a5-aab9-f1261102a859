const { v4: uuidv4 } = require('uuid');
const Logger = require('../utils/logger');

class ConversationManager {
    constructor(database, discordBot) {
        this.database = database;
        this.discordBot = discordBot;
        this.activeConversations = new Map(); // Cache for active conversations
        
        // Configuration
        this.maxChannelsPerUser = parseInt(process.env.MAX_CHANNELS_PER_USER) || 10;
        this.channelCleanupDays = parseInt(process.env.CHANNEL_CLEANUP_DAYS) || 7;
    }
    
    async initialize() {
        // Set up Discord bot event handlers
        if (this.discordBot.client) {
            this.discordBot.client.on('wowWhisperReply', async (data) => {
                await this.handleDiscordReply(data);
            });
        }
        
        // Load active conversations from database
        await this.loadActiveConversations();
        
        Logger.info('Conversation manager initialized');
    }
    
    async loadActiveConversations() {
        try {
            const conversations = await this.database.all(
                'SELECT * FROM conversations WHERE is_active = 1'
            );
            
            for (const conv of conversations) {
                this.activeConversations.set(conv.id, conv);
            }
            
            Logger.info(`Loaded ${conversations.length} active conversations`);
        } catch (error) {
            Logger.error('Failed to load active conversations:', error);
        }
    }
    
    generateConversationId(participant1, participant2) {
        // Create consistent ID regardless of message direction
        const p1 = `${participant1.name}-${participant1.realm}`;
        const p2 = `${participant2.name}-${participant2.realm}`;
        const participants = [p1, p2].sort();
        return participants.join('_').replace(/[^a-zA-Z0-9_-]/g, '');
    }
    
    async handleWoWMessage(messageData) {
        try {
            Logger.debug('Processing WoW message:', messageData);
            
            // Generate conversation ID
            const participant1 = {
                name: messageData.sender_name,
                realm: messageData.sender_realm,
                character: messageData.sender_character
            };
            
            const participant2 = {
                name: messageData.recipient_name,
                realm: messageData.recipient_realm,
                character: messageData.recipient_character
            };
            
            const conversationId = this.generateConversationId(participant1, participant2);
            messageData.conversation_id = conversationId;
            
            // Get or create conversation
            let conversation = this.activeConversations.get(conversationId);
            if (!conversation) {
                conversation = await this.getOrCreateConversation(conversationId, participant1, participant2);
            }
            
            // Save message to database
            await this.database.saveMessage({
                conversation_id: conversationId,
                sender_name: messageData.sender_name,
                sender_realm: messageData.sender_realm,
                sender_character: messageData.sender_character,
                recipient_name: messageData.recipient_name,
                recipient_realm: messageData.recipient_realm,
                recipient_character: messageData.recipient_character,
                message_content: messageData.message,
                message_type: messageData.type,
                timestamp: messageData.timestamp,
                wow_timestamp: messageData.timestamp
            });
            
            // Send to Discord
            if (conversation.discord_channel_id) {
                const discordMessage = await this.discordBot.sendMessageToChannel(
                    conversation.discord_channel_id,
                    {
                        sender_name: messageData.sender_name,
                        sender_realm: messageData.sender_realm,
                        sender_character: messageData.sender_character,
                        message_content: messageData.message,
                        message_type: messageData.type,
                        timestamp: messageData.timestamp
                    }
                );
                
                // Update message with Discord message ID
                if (discordMessage) {
                    // You could update the database record here if needed
                }
            }
            
            // Update conversation activity
            await this.database.updateConversationActivity(conversationId);
            
            Logger.info(`Processed message from ${messageData.sender_name} to ${messageData.recipient_name}`);
            
        } catch (error) {
            Logger.error('Failed to handle WoW message:', error);
            throw error;
        }
    }
    
    async getOrCreateConversation(conversationId, participant1, participant2) {
        try {
            // Check if conversation exists in database
            let conversation = await this.database.getConversation(conversationId);
            
            if (!conversation) {
                Logger.info(`Creating new conversation: ${conversationId}`);
                
                // Create Discord channel
                const discordChannel = await this.discordBot.createWhisperChannel({
                    id: conversationId,
                    participant1_name: participant1.name,
                    participant1_realm: participant1.realm,
                    participant1_character: participant1.character,
                    participant2_name: participant2.name,
                    participant2_realm: participant2.realm,
                    participant2_character: participant2.character
                });
                
                // Create conversation in database
                const conversationData = {
                    id: conversationId,
                    participant1_name: participant1.name,
                    participant1_realm: participant1.realm,
                    participant1_character: participant1.character,
                    participant2_name: participant2.name,
                    participant2_realm: participant2.realm,
                    participant2_character: participant2.character,
                    discord_channel_id: discordChannel.id
                };
                
                await this.database.createConversation(conversationData);
                conversation = conversationData;
                
                Logger.info(`Created conversation ${conversationId} with Discord channel ${discordChannel.id}`);
            }
            
            // Cache the conversation
            this.activeConversations.set(conversationId, conversation);
            
            return conversation;
            
        } catch (error) {
            Logger.error(`Failed to get or create conversation ${conversationId}:`, error);
            throw error;
        }
    }
    
    async handleDiscordReply(data) {
        try {
            Logger.debug('Processing Discord reply:', data);
            
            const conversation = this.activeConversations.get(data.conversationId);
            if (!conversation) {
                Logger.warn(`Conversation ${data.conversationId} not found for Discord reply`);
                return;
            }
            
            // Determine which WoW character should receive this message
            // This is a simplified version - you might want more sophisticated logic
            const targetCharacter = this.determineTargetCharacter(conversation, data.author);
            
            if (!targetCharacter) {
                Logger.warn('Could not determine target character for Discord reply');
                return;
            }
            
            // Save the Discord message to database
            await this.database.saveMessage({
                conversation_id: data.conversationId,
                sender_name: data.author.username,
                sender_realm: 'Discord',
                sender_character: data.author.username,
                recipient_name: targetCharacter.name,
                recipient_realm: targetCharacter.realm,
                recipient_character: targetCharacter.character,
                message_content: data.message,
                message_type: 'discord_reply',
                timestamp: Math.floor(Date.now() / 1000),
                discord_message_id: data.discordMessageId
            });
            
            // Here you would send the message back to WoW
            // This could be done through:
            // 1. Writing to a file that WoW addon monitors
            // 2. Using a webhook or HTTP endpoint that WoW addon polls
            // 3. Using a real-time connection like WebSocket
            
            await this.sendMessageToWoW(targetCharacter, data.message, data.conversationId);
            
            // Update conversation activity
            await this.database.updateConversationActivity(data.conversationId);
            
            Logger.info(`Processed Discord reply for conversation ${data.conversationId}`);
            
        } catch (error) {
            Logger.error('Failed to handle Discord reply:', error);
        }
    }
    
    determineTargetCharacter(conversation, discordAuthor) {
        // Simple logic: send to the character that didn't send the last message
        // In a real implementation, you might want to track which Discord user
        // corresponds to which WoW character, or use other logic
        
        // For now, assume we want to send to participant2 if they're not the sender
        // This is a placeholder - implement your own logic here
        
        return {
            name: conversation.participant2_name,
            realm: conversation.participant2_realm,
            character: conversation.participant2_character
        };
    }
    
    async sendMessageToWoW(targetCharacter, message, conversationId) {
        // This is where you'd implement sending messages back to WoW
        // Options include:
        
        // 1. File-based approach (WoW addon monitors a directory)
        const messageFile = {
            target: targetCharacter,
            message: message,
            conversationId: conversationId,
            timestamp: Date.now(),
            type: 'discord_reply'
        };
        
        // Write to a file that WoW addon can read
        // (This is a simplified approach)
        Logger.debug('Would send to WoW:', messageFile);
        
        // 2. HTTP webhook approach (if WoW addon can make HTTP requests)
        // 3. Database approach (WoW addon polls database)
        // 4. WebSocket approach (real-time communication)
        
        // For now, just log it
        Logger.info(`Sending message to WoW character ${targetCharacter.name}: ${message}`);
    }
    
    async getConversationHistory(conversationId, limit = 50) {
        try {
            const messages = await this.database.getConversationMessages(conversationId, limit);
            return messages.reverse(); // Return in chronological order
        } catch (error) {
            Logger.error(`Failed to get conversation history for ${conversationId}:`, error);
            throw error;
        }
    }
    
    async cleanupOldConversations() {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - this.channelCleanupDays);
            
            const oldConversations = await this.database.all(
                'SELECT * FROM conversations WHERE last_activity < ? AND is_active = 1',
                [cutoffDate.toISOString()]
            );
            
            for (const conversation of oldConversations) {
                Logger.info(`Cleaning up old conversation: ${conversation.id}`);
                
                // Delete Discord channel
                if (conversation.discord_channel_id) {
                    try {
                        await this.discordBot.deleteChannel(conversation.discord_channel_id);
                    } catch (error) {
                        Logger.warn(`Failed to delete Discord channel ${conversation.discord_channel_id}:`, error);
                    }
                }
                
                // Mark conversation as inactive
                await this.database.run(
                    'UPDATE conversations SET is_active = 0 WHERE id = ?',
                    [conversation.id]
                );
                
                // Remove from cache
                this.activeConversations.delete(conversation.id);
            }
            
            Logger.info(`Cleaned up ${oldConversations.length} old conversations`);
            
        } catch (error) {
            Logger.error('Failed to cleanup old conversations:', error);
        }
    }
}

module.exports = ConversationManager;
