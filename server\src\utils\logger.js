const fs = require('fs');
const path = require('path');

class Logger {
    constructor() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.debug = process.env.DEBUG === 'true';
        
        // Create logs directory if it doesn't exist
        this.logsDir = path.join(__dirname, '../../logs');
        if (!fs.existsSync(this.logsDir)) {
            fs.mkdirSync(this.logsDir, { recursive: true });
        }
        
        this.logFile = path.join(this.logsDir, `bridge-${new Date().toISOString().split('T')[0]}.log`);
    }
    
    formatMessage(level, message, data = null) {
        const timestamp = new Date().toISOString();
        let logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
        
        if (data) {
            if (typeof data === 'object') {
                logMessage += '\n' + JSON.stringify(data, null, 2);
            } else {
                logMessage += ' ' + data;
            }
        }
        
        return logMessage;
    }
    
    writeToFile(message) {
        try {
            fs.appendFileSync(this.logFile, message + '\n');
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    
    log(level, message, data = null) {
        const formattedMessage = this.formatMessage(level, message, data);
        
        // Always write to file
        this.writeToFile(formattedMessage);
        
        // Console output based on level
        switch (level) {
            case 'error':
                console.error(formattedMessage);
                break;
            case 'warn':
                console.warn(formattedMessage);
                break;
            case 'info':
                console.log(formattedMessage);
                break;
            case 'debug':
                if (this.debug) {
                    console.log(formattedMessage);
                }
                break;
            default:
                console.log(formattedMessage);
        }
    }
    
    error(message, data = null) {
        this.log('error', message, data);
    }
    
    warn(message, data = null) {
        this.log('warn', message, data);
    }
    
    info(message, data = null) {
        this.log('info', message, data);
    }
    
    debug(message, data = null) {
        this.log('debug', message, data);
    }
}

module.exports = new Logger();
