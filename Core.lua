-- Main core file for Discord Bridge addon
DiscordBridge = DiscordBridge or {}

-- Initialize addon
local frame = Create<PERSON>rame("Frame")
local messageQueue = {}
local isProcessingQueue = false
local lastSendTime = 0

-- Event handler
local function OnEvent(self, event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        if addonName == "DiscordBridge" then
            DiscordBridge:Initialize()
        end
    elseif event == "CHAT_MSG_WHISPER" then
        DiscordBridge:OnWhisperReceived(...)
    elseif event == "CHAT_MSG_WHISPER_INFORM" then
        DiscordBridge:OnWhisperSent(...)
    end
end

-- Initialize the addon
function DiscordBridge:Initialize()
    DiscordBridge.Config:Initialize()

    if DiscordBridge.Config:Get("enabled") then
        DiscordBridge.Utils:Print("Discord Bridge loaded successfully!")
        DiscordBridge.Utils:DebugPrint("Character: " .. DiscordBridge.Config:Get("characterName"))
        DiscordBridge.Utils:DebugPrint("Realm: " .. DiscordBridge.Config:Get("realmName"))
        DiscordBridge.Utils:DebugPrint("API Endpoint: " .. DiscordBridge.Config:Get("apiEndpoint"))
    else
        DiscordBridge.Utils:Print("Discord Bridge is disabled in configuration")
    end
end

-- Handle incoming whispers
function DiscordBridge:OnWhisperReceived(message, sender, language, channelString, target, flags, unknown,
    channelNumber, channelName, unknown2, counter, guid)
    if not DiscordBridge.Config:Get("enabled") then
        return
    end

    local senderName = DiscordBridge.Utils:CleanPlayerName(sender)
    local senderRealm = DiscordBridge.Utils:GetPlayerRealm(sender)
    local recipientName = DiscordBridge.Config:Get("characterName")
    local recipientRealm = DiscordBridge.Config:Get("realmName")

    local messageData = {
        type = "whisper_received",
        senderName = senderName,
        senderRealm = senderRealm,
        senderCharacter = senderName,
        recipientName = recipientName,
        recipientRealm = recipientRealm,
        recipientCharacter = recipientName,
        message = message,
        timestamp = DiscordBridge.Utils:GetTimestamp(),
        conversationId = DiscordBridge.Utils:GenerateConversationId(senderName, senderRealm, recipientName,
            recipientRealm)
    }

    DiscordBridge.Utils:DebugPrint("Received whisper from " .. senderName .. ": " .. message)
    self:QueueMessage(messageData)
end

-- Handle outgoing whispers
function DiscordBridge:OnWhisperSent(message, target, language, channelString, unknown, flags, unknown2, channelNumber,
    channelName, unknown3, counter, guid)
    if not DiscordBridge.Config:Get("enabled") then
        return
    end

    local senderName = DiscordBridge.Config:Get("characterName")
    local senderRealm = DiscordBridge.Config:Get("realmName")
    local recipientName = DiscordBridge.Utils:CleanPlayerName(target)
    local recipientRealm = DiscordBridge.Utils:GetPlayerRealm(target)

    local messageData = {
        type = "whisper_sent",
        senderName = senderName,
        senderRealm = senderRealm,
        senderCharacter = senderName,
        recipientName = recipientName,
        recipientRealm = recipientRealm,
        recipientCharacter = recipientName,
        message = message,
        timestamp = DiscordBridge.Utils:GetTimestamp(),
        conversationId = DiscordBridge.Utils:GenerateConversationId(senderName, senderRealm, recipientName,
            recipientRealm)
    }

    DiscordBridge.Utils:DebugPrint("Sent whisper to " .. recipientName .. ": " .. message)
    self:QueueMessage(messageData)
end

-- Queue message for sending
function DiscordBridge:QueueMessage(messageData)
    table.insert(messageQueue, messageData)

    -- Limit queue size
    local maxSize = DiscordBridge.Config:Get("messageQueueSize")
    while #messageQueue > maxSize do
        table.remove(messageQueue, 1)
    end

    if not isProcessingQueue then
        self:ProcessMessageQueue()
    end
end

-- Process message queue
function DiscordBridge:ProcessMessageQueue()
    if #messageQueue == 0 then
        isProcessingQueue = false
        return
    end

    isProcessingQueue = true
    local currentTime = GetTime()
    local sendInterval = DiscordBridge.Config:Get("sendInterval")

    if currentTime - lastSendTime >= sendInterval then
        local messageData = table.remove(messageQueue, 1)
        self:SendToAPI(messageData)
        lastSendTime = currentTime
    end

    -- Schedule next processing
    C_Timer.After(0.1, function()
        self:ProcessMessageQueue()
    end)
end

-- Send message to API
function DiscordBridge:SendToAPI(messageData, retryCount)
    retryCount = retryCount or 0
    local maxRetries = DiscordBridge.Config:Get("maxRetries")

    if retryCount > maxRetries then
        DiscordBridge.Utils:Print("Failed to send message after " .. maxRetries .. " retries")
        return
    end

    local payload = DiscordBridge.Utils:CreateMessagePayload(messageData)
    local endpoint = DiscordBridge.Config:Get("apiEndpoint")

    DiscordBridge.Utils:DebugPrint("Sending to API: " .. endpoint)
    DiscordBridge.Utils:DebugPrint("Payload: " .. payload)

    -- Use HTTP request (this is a simplified version - in real implementation you'd need an HTTP library)
    -- For now, we'll use a simple file-based approach or external tool
    self:SendHTTPRequest(endpoint, payload, function(success, response)
        if success then
            DiscordBridge.Utils:DebugPrint("Message sent successfully")
        else
            DiscordBridge.Utils:DebugPrint("Failed to send message, retrying... (" .. (retryCount + 1) .. "/" ..
                                               maxRetries .. ")")
            local retryDelay = DiscordBridge.Config:Get("retryDelay")
            C_Timer.After(retryDelay, function()
                self:SendToAPI(messageData, retryCount + 1)
            end)
        end
    end)
end

-- HTTP request function (simplified - would need proper implementation)
function DiscordBridge:SendHTTPRequest(url, data, callback)
    -- This is a placeholder - WoW doesn't have built-in HTTP support
    -- You would need to use an external tool or addon like WeakAuras HTTP library
    -- For now, we'll write to a file that the Node.js server can monitor

    local filename = "Interface\\AddOns\\DiscordBridge\\messages\\" .. GetTime() .. ".json"

    -- Create directory if it doesn't exist (this won't work in WoW, just for reference)
    -- In practice, you'd need to pre-create the directory or use a different approach

    -- Write message to file (this also won't work directly in WoW due to security restrictions)
    -- You'd need to use SavedVariables or another method

    -- For demonstration, we'll add to a queue in SavedVariables
    if not DiscordBridgeDB.pendingMessages then
        DiscordBridgeDB.pendingMessages = {}
    end

    table.insert(DiscordBridgeDB.pendingMessages, {
        url = url,
        data = data,
        timestamp = GetTime()
    })

    -- Simulate success for now
    if callback then
        callback(true, "OK")
    end
end

-- Slash commands
SLASH_DISCORDBRIDGE1 = "/discordbridge"
SLASH_DISCORDBRIDGE2 = "/db"

function SlashCmdList.DISCORDBRIDGE(msg)
    local command, arg = string.match(msg, "^(%S+)%s*(.*)$")
    command = command or msg

    if command == "enable" then
        DiscordBridge.Config:Set("enabled", true)
        DiscordBridge.Utils:Print("Discord Bridge enabled")
    elseif command == "disable" then
        DiscordBridge.Config:Set("enabled", false)
        DiscordBridge.Utils:Print("Discord Bridge disabled")
    elseif command == "debug" then
        local debugMode = not DiscordBridge.Config:Get("debug")
        DiscordBridge.Config:Set("debug", debugMode)
        DiscordBridge.Utils:Print("Debug mode " .. (debugMode and "enabled" or "disabled"))
    elseif command == "status" then
        DiscordBridge.Utils:Print("Status:")
        DiscordBridge.Utils:Print("  Enabled: " .. tostring(DiscordBridge.Config:Get("enabled")))
        DiscordBridge.Utils:Print("  Debug: " .. tostring(DiscordBridge.Config:Get("debug")))
        DiscordBridge.Utils:Print("  Character: " .. DiscordBridge.Config:Get("characterName"))
        DiscordBridge.Utils:Print("  Realm: " .. DiscordBridge.Config:Get("realmName"))
        DiscordBridge.Utils:Print("  Queue size: " .. #messageQueue)
        if DiscordBridgeDB.pendingMessages then
            DiscordBridge.Utils:Print("  Pending messages: " .. #DiscordBridgeDB.pendingMessages)
        end
    elseif command == "test" then
        DiscordBridge.Utils:Print("Sending test message...")
        local testData = {
            type = "test",
            senderName = "TestSender",
            senderRealm = "TestRealm",
            senderCharacter = "TestSender",
            recipientName = DiscordBridge.Config:Get("characterName"),
            recipientRealm = DiscordBridge.Config:Get("realmName"),
            recipientCharacter = DiscordBridge.Config:Get("characterName"),
            message = "This is a test message",
            timestamp = DiscordBridge.Utils:GetTimestamp(),
            conversationId = "test_conversation"
        }
        DiscordBridge:QueueMessage(testData)
    else
        DiscordBridge.Utils:Print("Commands:")
        DiscordBridge.Utils:Print("  /db enable - Enable the addon")
        DiscordBridge.Utils:Print("  /db disable - Disable the addon")
        DiscordBridge.Utils:Print("  /db debug - Toggle debug mode")
        DiscordBridge.Utils:Print("  /db status - Show current status")
        DiscordBridge.Utils:Print("  /db test - Send a test message")
    end
end

-- Register events
frame:RegisterEvent("ADDON_LOADED")
frame:RegisterEvent("CHAT_MSG_WHISPER")
frame:RegisterEvent("CHAT_MSG_WHISPER_INFORM")
frame:SetScript("OnEvent", OnEvent)
