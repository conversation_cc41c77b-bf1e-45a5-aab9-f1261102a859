# WoW Discord Bridge

Система для интеграции личных сообщений World of Warcraft с Discord каналами. Автоматически создает уникальные текстовые каналы в Discord для каждого собеседника из игры.

## Возможности

- 🎮 **Мультиаккаунт поддержка**: Работает с несколькими окнами WoW одновременно
- 💬 **Автоматические каналы**: Создает уникальный Discord канал для каждого собеседника
- 🔄 **Двусторонняя синхронизация**: Сообщения из WoW попадают в Discord и наоборот
- 🏷️ **Умная идентификация**: Уникальные идентификаторы для каждого разговора
- 📊 **База данных**: Сохранение истории сообщений и управление разговорами
- ⚡ **Реальное время**: Мгновенная доставка сообщений

## Архитектура

```
WoW Addon → Node.js Server → Discord Bot
     ↑                            ↓
     └── SQLite Database ←────────┘
```

### Компоненты:

1. **WoW Addon** (`DiscordBridge.lua`) - Перехватывает личные сообщения в игре
2. **Node.js Server** - API сервер для обработки сообщений
3. **Discord Bot** - Создает каналы и управляет сообщениями в Discord
4. **SQLite Database** - Хранит историю разговоров и сообщений

## Установка

### 1. Настройка WoW Addon

1. Скопируйте папку с аддоном в `Interface/AddOns/DiscordBridge/`
2. Перезапустите WoW или выполните `/reload`
3. Убедитесь, что аддон загружен: `/db status`

### 2. Настройка Discord Bot

1. Создайте Discord приложение на https://discord.com/developers/applications
2. Создайте бота и получите токен
3. Пригласите бота на ваш сервер с правами:
   - Manage Channels
   - Send Messages
   - Read Message History
   - View Channels

### 3. Установка Node.js Server

```bash
cd server
npm install
cp .env.example .env
```

### 4. Конфигурация

Отредактируйте файл `.env`:

```env
# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_bot_token_here
DISCORD_GUILD_ID=your_server_id_here
DISCORD_CATEGORY_ID=your_category_id_for_channels

# Server Configuration
PORT=3000
HOST=localhost

# Database Configuration
DATABASE_PATH=./data/conversations.db

# WoW Configuration
WOW_ADDON_DATA_PATH=../WTF/Account/YOUR_ACCOUNT/SavedVariables/DiscordBridge.lua
```

### 5. Запуск

```bash
# Разработка
npm run dev

# Продакшн
npm start
```

## Использование

### Команды WoW Addon

- `/db enable` - Включить аддон
- `/db disable` - Выключить аддон
- `/db debug` - Переключить режим отладки
- `/db status` - Показать статус
- `/db test` - Отправить тестовое сообщение

### API Endpoints

- `GET /health` - Проверка состояния сервера
- `POST /api/message` - Получение сообщений от WoW
- `GET /api/conversations` - Список активных разговоров
- `GET /api/conversations/:id/messages` - История сообщений
- `GET /api/status` - Статус системы

### Как это работает

1. **Получение сообщения в WoW**: Аддон перехватывает входящие/исходящие личные сообщения
2. **Отправка в API**: Сообщение отправляется на Node.js сервер через HTTP API
3. **Создание канала**: Если разговор новый, создается уникальный Discord канал
4. **Отправка в Discord**: Сообщение появляется в соответствующем канале
5. **Ответ из Discord**: Сообщения из Discord канала отправляются обратно в WoW

## Структура проекта

```
├── DiscordBridge.toc          # Описание WoW аддона
├── Core.lua                   # Основная логика аддона
├── Config.lua                 # Конфигурация аддона
├── Utils.lua                  # Утилиты аддона
├── server/                    # Node.js сервер
│   ├── package.json
│   ├── index.js              # Главный файл сервера
│   ├── src/
│   │   ├── discord/
│   │   │   └── bot.js        # Discord бот
│   │   ├── database/
│   │   │   └── database.js   # Работа с БД
│   │   ├── managers/
│   │   │   └── conversationManager.js
│   │   ├── routes/
│   │   │   └── api.js        # API роуты
│   │   ├── utils/
│   │   │   └── logger.js     # Логирование
│   │   └── wow/
│   │       └── dataWatcher.js # Мониторинг WoW данных
│   └── .env.example          # Пример конфигурации
└── README.md                 # Документация
```

## Конфигурация

### WoW Addon

Настройки хранятся в `SavedVariables/DiscordBridge.lua`:

```lua
DiscordBridgeDB = {
    ["enabled"] = true,
    ["debug"] = false,
    ["apiEndpoint"] = "http://localhost:3000/api/message",
    ["characterName"] = "YourCharacter",
    ["realmName"] = "YourRealm"
}
```

### Discord Bot

Основные настройки в `.env` файле. Бот автоматически:
- Создает каналы в указанной категории
- Устанавливает права доступа
- Форматирует сообщения с информацией об отправителе

## Безопасность

- Используйте HTTPS в продакшн среде
- Ограничьте доступ к API по IP
- Регулярно обновляйте токены Discord
- Настройте права доступа к Discord каналам

## Устранение неполадок

### WoW Addon не отправляет сообщения

1. Проверьте статус: `/db status`
2. Включите отладку: `/db debug`
3. Проверьте настройки в SavedVariables
4. Убедитесь, что сервер запущен

### Discord бот не создает каналы

1. Проверьте права бота на сервере
2. Убедитесь, что указана правильная категория
3. Проверьте логи сервера
4. Проверьте токен и ID сервера

### Сообщения не синхронизируются

1. Проверьте подключение к базе данных
2. Убедитесь, что все сервисы запущены
3. Проверьте логи на ошибки
4. Протестируйте API endpoint

## Разработка

### Требования

- Node.js 16+
- World of Warcraft (Retail)
- Discord сервер с правами администратора

### Тестирование

```bash
# Тест API
curl -X POST http://localhost:3000/api/test

# Тест WoW аддона
/db test
```

## Лицензия

MIT License - см. файл LICENSE для деталей.

## Поддержка

Если у вас возникли проблемы:

1. Проверьте логи сервера в `server/logs/`
2. Включите режим отладки в WoW: `/db debug`
3. Проверьте статус всех компонентов: `GET /api/status`
4. Создайте issue с подробным описанием проблемы
