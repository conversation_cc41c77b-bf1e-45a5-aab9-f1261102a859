const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// Import modules
const DiscordBot = require('./src/discord/bot');
const Database = require('./src/database/database');
const WoWDataWatcher = require('./src/wow/dataWatcher');
const ConversationManager = require('./src/managers/conversationManager');
const Logger = require('./src/utils/logger');

class WoWDiscordBridge {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.host = process.env.HOST || 'localhost';

        this.database = null;
        this.discordBot = null;
        this.wowDataWatcher = null;
        this.conversationManager = null;

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // CORS configuration
        this.app.use(cors({
            origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
            credentials: true
        }));

        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // Logging middleware
        this.app.use((req, res, next) => {
            Logger.info(`${req.method} ${req.path} - ${req.ip}`);
            next();
        });

        // Error handling middleware
        this.app.use((err, req, res, next) => {
            Logger.error('Express error:', err);
            res.status(500).json({ error: 'Internal server error' });
        });
    }

    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                uptime: process.uptime()
            });
        });

        // API routes
        this.app.use('/api', require('./src/routes/api'));

        // Static files (for web interface)
        this.app.use('/static', express.static(path.join(__dirname, 'public')));

        // Web dashboard route
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        // API info route
        this.app.get('/info', (req, res) => {
            res.json({
                name: 'WoW Discord Bridge',
                version: '1.0.0',
                status: 'running',
                endpoints: {
                    health: '/health',
                    api: '/api',
                    message: '/api/message',
                    dashboard: '/'
                }
            });
        });

        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({ error: 'Endpoint not found' });
        });
    }

    async initialize() {
        try {
            Logger.info('Initializing WoW Discord Bridge...');

            // Initialize database
            this.database = new Database();
            await this.database.initialize();
            Logger.info('Database initialized');

            // Initialize Discord bot
            this.discordBot = new DiscordBot();
            await this.discordBot.initialize();
            Logger.info('Discord bot initialized');

            // Initialize conversation manager
            this.conversationManager = new ConversationManager(this.database, this.discordBot);
            await this.conversationManager.initialize();
            Logger.info('Conversation manager initialized');

            // Initialize WoW data watcher
            this.wowDataWatcher = new WoWDataWatcher(this.conversationManager);
            await this.wowDataWatcher.initialize();
            Logger.info('WoW data watcher initialized');

            // Make managers available to routes
            this.app.locals.conversationManager = this.conversationManager;
            this.app.locals.database = this.database;
            this.app.locals.discordBot = this.discordBot;

            Logger.info('All components initialized successfully');

        } catch (error) {
            Logger.error('Failed to initialize:', error);
            throw error;
        }
    }

    async start() {
        try {
            await this.initialize();

            this.server = this.app.listen(this.port, this.host, () => {
                Logger.info(`WoW Discord Bridge server running on http://${this.host}:${this.port}`);
                Logger.info('Ready to bridge WoW whispers to Discord!');
            });

            // Graceful shutdown
            process.on('SIGTERM', () => this.shutdown());
            process.on('SIGINT', () => this.shutdown());

        } catch (error) {
            Logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }

    async shutdown() {
        Logger.info('Shutting down WoW Discord Bridge...');

        if (this.server) {
            this.server.close();
        }

        if (this.wowDataWatcher) {
            await this.wowDataWatcher.stop();
        }

        if (this.discordBot) {
            await this.discordBot.destroy();
        }

        if (this.database) {
            await this.database.close();
        }

        Logger.info('Shutdown complete');
        process.exit(0);
    }
}

// Start the application
const bridge = new WoWDiscordBridge();
bridge.start().catch(error => {
    Logger.error('Failed to start application:', error);
    process.exit(1);
});
