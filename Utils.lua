-- Utility functions for Discord Bridge addon
DiscordBridge = DiscordBridge or {}
DiscordBridge.Utils = {}

-- Debug print function
function DiscordBridge.Utils:DebugPrint(message)
    if DiscordBridge.Config:Get("debug") then
        print("|cFF00FF00[DiscordBridge]|r " .. tostring(message))
    end
end

-- Regular print function
function DiscordBridge.Utils:Print(message)
    print("|cFF00FF00[DiscordBridge]|r " .. tostring(message))
end

-- Generate unique identifier for a conversation
function DiscordBridge.Utils:GenerateConversationId(senderName, senderRealm, recipientName, recipientRealm)
    -- Create a consistent ID regardless of who sends the message
    local participants = {senderName .. "-" .. (senderRealm or ""), recipientName .. "-" .. (recipientRealm or "")}
    table.sort(participants)
    return table.concat(participants, "_")
end

-- Clean player name (remove server suffix if present)
function DiscordBridge.Utils:CleanPlayerName(fullName)
    if not fullName then return "" end
    local name = string.match(fullName, "^([^-]+)")
    return name or fullName
end

-- Get player realm from full name
function DiscordBridge.Utils:GetPlayerRealm(fullName)
    if not fullName then return GetRealmName() end
    local realm = string.match(fullName, "-(.+)$")
    return realm or GetRealmName()
end

-- Format timestamp
function DiscordBridge.Utils:GetTimestamp()
    return time()
end

-- Escape special characters for JSON
function DiscordBridge.Utils:EscapeJson(str)
    if not str then return "" end
    str = string.gsub(str, "\\", "\\\\")
    str = string.gsub(str, '"', '\\"')
    str = string.gsub(str, "\n", "\\n")
    str = string.gsub(str, "\r", "\\r")
    str = string.gsub(str, "\t", "\\t")
    return str
end

-- Create JSON payload for API
function DiscordBridge.Utils:CreateMessagePayload(messageData)
    local payload = string.format([[{
        "type": "%s",
        "sender": {
            "name": "%s",
            "realm": "%s",
            "character": "%s"
        },
        "recipient": {
            "name": "%s",
            "realm": "%s",
            "character": "%s"
        },
        "message": "%s",
        "timestamp": %d,
        "conversationId": "%s"
    }]], 
        messageData.type or "whisper",
        self:EscapeJson(messageData.senderName),
        self:EscapeJson(messageData.senderRealm),
        self:EscapeJson(messageData.senderCharacter),
        self:EscapeJson(messageData.recipientName),
        self:EscapeJson(messageData.recipientRealm),
        self:EscapeJson(messageData.recipientCharacter),
        self:EscapeJson(messageData.message),
        messageData.timestamp,
        self:EscapeJson(messageData.conversationId)
    )
    
    return payload
end
