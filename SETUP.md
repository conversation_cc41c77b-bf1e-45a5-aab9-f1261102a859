# Пошаговая установка WoW Discord Bridge

## Предварительные требования

- World of Warcraft (Retail версия)
- Node.js 16 или выше
- Discord сервер с правами администратора
- Базовые знания работы с командной строкой

## Шаг 1: Настройка Discord

### 1.1 Создание Discord приложения

1. Перейдите на https://discord.com/developers/applications
2. Нажмите "New Application"
3. Введите название (например, "WoW Bridge")
4. Сохраните Application ID

### 1.2 Создание бота

1. В левом меню выберите "Bot"
2. Нажмите "Add Bot"
3. Скопируйте Bot Token (понадобится позже)
4. Включите следующие Privileged Gateway Intents:
   - Message Content Intent

### 1.3 Настройка прав

В разделе "Bot Permissions" выберите:
- [x] Manage Channels
- [x] View Channels  
- [x] Send Messages
- [x] Read Message History
- [x] Use Slash Commands

### 1.4 Приглашение бота на сервер

1. В разделе "OAuth2" → "URL Generator"
2. Выберите scope: "bot"
3. Выберите те же права, что и выше
4. Скопируйте сгенерированную ссылку
5. Перейдите по ссылке и пригласите бота на ваш сервер

### 1.5 Подготовка Discord сервера

1. Создайте категорию для каналов шепота (например, "WoW Whispers")
2. Скопируйте ID категории (ПКМ → Copy ID)
3. Скопируйте ID сервера (ПКМ на название сервера → Copy ID)

## Шаг 2: Установка WoW Addon

### 2.1 Копирование файлов

1. Скопируйте все файлы аддона в папку:
   ```
   World of Warcraft\_retail_\Interface\AddOns\DiscordBridge\
   ```

2. Структура должна выглядеть так:
   ```
   DiscordBridge\
   ├── DiscordBridge.toc
   ├── Core.lua
   ├── Config.lua
   └── Utils.lua
   ```

### 2.2 Активация аддона

1. Запустите World of Warcraft
2. В меню выбора персонажа нажмите "AddOns"
3. Найдите "Discord Bridge" и убедитесь, что он включен
4. Войдите в игру

### 2.3 Проверка работы

1. В чате введите: `/db status`
2. Должно появиться сообщение о статусе аддона
3. Если аддон не загружен, проверьте файлы и перезапустите игру

## Шаг 3: Установка Node.js сервера

### 3.1 Установка зависимостей

```bash
cd server
npm install
```

### 3.2 Настройка конфигурации

1. Скопируйте файл конфигурации:
   ```bash
   cp .env.example .env
   ```

2. Отредактируйте `.env` файл:
   ```env
   # Discord Bot Configuration
   DISCORD_BOT_TOKEN=ваш_токен_бота_здесь
   DISCORD_GUILD_ID=id_вашего_сервера
   DISCORD_CATEGORY_ID=id_категории_для_каналов

   # Server Configuration
   PORT=3000
   HOST=localhost

   # Database Configuration
   DATABASE_PATH=./data/conversations.db

   # WoW Configuration (замените YOUR_ACCOUNT на ваш аккаунт)
   WOW_ADDON_DATA_PATH=../WTF/Account/YOUR_ACCOUNT/SavedVariables/DiscordBridge.lua

   # Logging
   LOG_LEVEL=info
   DEBUG=false
   ```

### 3.3 Создание необходимых папок

```bash
mkdir -p data
mkdir -p logs
mkdir -p messages
```

## Шаг 4: Первый запуск

### 4.1 Запуск сервера

```bash
# Для разработки (с автоперезагрузкой)
npm run dev

# Или для обычного запуска
npm start
```

### 4.2 Проверка работы сервера

1. Откройте браузер и перейдите на http://localhost:3000
2. Вы должны увидеть JSON ответ с информацией о сервере
3. Проверьте статус: http://localhost:3000/api/status

### 4.3 Проверка подключения Discord

В логах сервера должны появиться сообщения:
```
[INFO] Discord bot logged in as YourBot#1234
[INFO] Connected to guild: Your Server Name
[INFO] Discord bot is ready to create channels
```

## Шаг 5: Тестирование системы

### 5.1 Тест из WoW

1. В игре введите: `/db test`
2. Проверьте логи сервера на наличие сообщения
3. В Discord должен появиться новый канал

### 5.2 Тест API

```bash
curl -X POST http://localhost:3000/api/test
```

### 5.3 Тест реального сообщения

1. Попросите кого-то написать вам в игре
2. Или напишите кому-то сами
3. Проверьте, появился ли канал в Discord

## Шаг 6: Настройка для нескольких аккаунтов

### 6.1 Для каждого дополнительного аккаунта WoW

1. Установите аддон на каждый аккаунт
2. Убедитесь, что каждый аккаунт имеет уникальное имя персонажа

### 6.2 Настройка мониторинга

Сервер автоматически найдет все файлы SavedVariables для разных аккаунтов в:
```
WTF/Account/*/SavedVariables/DiscordBridge.lua
```

## Устранение проблем

### Проблема: Аддон не загружается

**Решение:**
1. Проверьте правильность пути к файлам
2. Убедитесь, что файл .toc имеет правильную кодировку
3. Проверьте версию интерфейса в .toc файле

### Проблема: Сервер не запускается

**Решение:**
1. Проверьте, что Node.js установлен: `node --version`
2. Убедитесь, что все зависимости установлены: `npm install`
3. Проверьте файл .env на наличие всех необходимых переменных

### Проблема: Discord бот не подключается

**Решение:**
1. Проверьте правильность токена бота
2. Убедитесь, что бот приглашен на сервер
3. Проверьте права бота

### Проблема: Каналы не создаются

**Решение:**
1. Проверьте права бота на создание каналов
2. Убедитесь, что ID категории правильный
3. Проверьте логи сервера на ошибки

### Проблема: Сообщения не передаются

**Решение:**
1. Проверьте настройки аддона: `/db status`
2. Включите отладку: `/db debug`
3. Проверьте подключение к API серверу
4. Убедитесь, что порт 3000 не заблокирован

## Дополнительные настройки

### Автозапуск сервера (Windows)

Создайте bat файл `start_bridge.bat`:
```batch
@echo off
cd /d "путь_к_папке_server"
npm start
pause
```

### Автозапуск сервера (Linux/Mac)

Создайте systemd сервис или используйте PM2:
```bash
npm install -g pm2
pm2 start index.js --name "wow-discord-bridge"
pm2 startup
pm2 save
```

### Настройка HTTPS (для продакшн)

1. Получите SSL сертификат
2. Обновите конфигурацию сервера
3. Измените URL в настройках аддона

## Поддержка

Если у вас остались вопросы:

1. Проверьте логи в папке `server/logs/`
2. Включите режим отладки в WoW и сервере
3. Проверьте все ID и токены
4. Убедитесь, что все порты открыты

Система готова к использованию! Теперь все личные сообщения из WoW будут автоматически создавать каналы в Discord.
