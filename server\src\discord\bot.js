const { Client, GatewayIntentBits, ChannelType, PermissionFlagsBits } = require('discord.js');
const Logger = require('../utils/logger');

class DiscordBot {
    constructor() {
        this.client = null;
        this.guild = null;
        this.category = null;
        this.isReady = false;
        
        this.token = process.env.DISCORD_BOT_TOKEN;
        this.guildId = process.env.DISCORD_GUILD_ID;
        this.categoryId = process.env.DISCORD_CATEGORY_ID;
        this.channelPrefix = process.env.CHANNEL_NAME_PREFIX || 'whisper-';
        
        if (!this.token || !this.guildId) {
            throw new Error('Discord bot token and guild ID are required');
        }
    }
    
    async initialize() {
        try {
            this.client = new Client({
                intents: [
                    GatewayIntentBits.Guilds,
                    GatewayIntentBits.GuildMessages,
                    GatewayIntentBits.MessageContent,
                    GatewayIntentBits.GuildMembers
                ]
            });
            
            this.setupEventHandlers();
            
            await this.client.login(this.token);
            
            // Wait for ready event
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Discord bot failed to become ready within 30 seconds'));
                }, 30000);
                
                this.client.once('ready', () => {
                    clearTimeout(timeout);
                    resolve();
                });
            });
            
            Logger.info('Discord bot initialized successfully');
            
        } catch (error) {
            Logger.error('Failed to initialize Discord bot:', error);
            throw error;
        }
    }
    
    setupEventHandlers() {
        this.client.on('ready', async () => {
            Logger.info(`Discord bot logged in as ${this.client.user.tag}`);
            
            try {
                // Get guild
                this.guild = await this.client.guilds.fetch(this.guildId);
                Logger.info(`Connected to guild: ${this.guild.name}`);
                
                // Get or create category
                if (this.categoryId) {
                    this.category = await this.guild.channels.fetch(this.categoryId);
                    if (!this.category || this.category.type !== ChannelType.GuildCategory) {
                        Logger.warn('Specified category not found or invalid, will create channels without category');
                        this.category = null;
                    }
                }
                
                this.isReady = true;
                Logger.info('Discord bot is ready to create channels');
                
            } catch (error) {
                Logger.error('Error during Discord bot setup:', error);
            }
        });
        
        this.client.on('messageCreate', async (message) => {
            // Handle messages from Discord channels (for sending back to WoW)
            if (message.author.bot) return;
            
            try {
                await this.handleDiscordMessage(message);
            } catch (error) {
                Logger.error('Error handling Discord message:', error);
            }
        });
        
        this.client.on('error', (error) => {
            Logger.error('Discord client error:', error);
        });
        
        this.client.on('warn', (warning) => {
            Logger.warn('Discord client warning:', warning);
        });
    }
    
    async handleDiscordMessage(message) {
        // Check if this is a whisper channel
        if (!message.channel.name.startsWith(this.channelPrefix)) {
            return;
        }
        
        // Extract conversation info from channel name or topic
        const conversationId = this.extractConversationId(message.channel);
        if (!conversationId) {
            Logger.warn(`Could not extract conversation ID from channel: ${message.channel.name}`);
            return;
        }
        
        // This will be handled by the conversation manager
        // For now, just log it
        Logger.debug(`Discord message in ${message.channel.name}: ${message.content}`);
        
        // Emit event for conversation manager to handle
        this.client.emit('wowWhisperReply', {
            conversationId,
            message: message.content,
            author: message.author,
            channel: message.channel,
            discordMessageId: message.id
        });
    }
    
    extractConversationId(channel) {
        // Try to get from channel topic first
        if (channel.topic) {
            const match = channel.topic.match(/ID:\s*([a-zA-Z0-9_-]+)/);
            if (match) {
                return match[1];
            }
        }
        
        // Fallback to channel name parsing
        const nameWithoutPrefix = channel.name.replace(this.channelPrefix, '');
        return nameWithoutPrefix || null;
    }
    
    async createWhisperChannel(conversationData) {
        if (!this.isReady) {
            throw new Error('Discord bot is not ready');
        }
        
        try {
            const channelName = this.generateChannelName(conversationData);
            const channelTopic = this.generateChannelTopic(conversationData);
            
            Logger.info(`Creating Discord channel: ${channelName}`);
            
            const channel = await this.guild.channels.create({
                name: channelName,
                type: ChannelType.GuildText,
                topic: channelTopic,
                parent: this.category?.id || null,
                permissionOverwrites: [
                    {
                        id: this.guild.roles.everyone.id,
                        deny: [PermissionFlagsBits.ViewChannel]
                    },
                    // Add permissions for specific roles/users here if needed
                ]
            });
            
            // Send initial message
            const embed = {
                title: '💬 New Whisper Conversation',
                description: `Conversation between **${conversationData.participant1_name}** and **${conversationData.participant2_name}**`,
                fields: [
                    {
                        name: 'Participant 1',
                        value: `${conversationData.participant1_name} (${conversationData.participant1_realm})`,
                        inline: true
                    },
                    {
                        name: 'Participant 2',
                        value: `${conversationData.participant2_name} (${conversationData.participant2_realm})`,
                        inline: true
                    },
                    {
                        name: 'Conversation ID',
                        value: conversationData.id,
                        inline: false
                    }
                ],
                color: 0x00ff00,
                timestamp: new Date().toISOString()
            };
            
            await channel.send({ embeds: [embed] });
            
            Logger.info(`Created Discord channel: ${channel.name} (${channel.id})`);
            return channel;
            
        } catch (error) {
            Logger.error('Failed to create Discord channel:', error);
            throw error;
        }
    }
    
    generateChannelName(conversationData) {
        // Create a clean channel name
        const participant1 = conversationData.participant1_name.toLowerCase().replace(/[^a-z0-9]/g, '');
        const participant2 = conversationData.participant2_name.toLowerCase().replace(/[^a-z0-9]/g, '');
        
        // Sort names to ensure consistent naming
        const names = [participant1, participant2].sort();
        
        return `${this.channelPrefix}${names[0]}-${names[1]}`;
    }
    
    generateChannelTopic(conversationData) {
        return `Whisper conversation between ${conversationData.participant1_name} (${conversationData.participant1_realm}) and ${conversationData.participant2_name} (${conversationData.participant2_realm}) | ID: ${conversationData.id}`;
    }
    
    async sendMessageToChannel(channelId, messageData) {
        if (!this.isReady) {
            throw new Error('Discord bot is not ready');
        }
        
        try {
            const channel = await this.client.channels.fetch(channelId);
            if (!channel) {
                throw new Error(`Channel ${channelId} not found`);
            }
            
            const embed = {
                author: {
                    name: `${messageData.sender_name} (${messageData.sender_realm})`,
                    icon_url: this.getCharacterAvatar(messageData.sender_name, messageData.sender_realm)
                },
                description: messageData.message_content,
                color: messageData.message_type === 'whisper_sent' ? 0x0099ff : 0xff9900,
                timestamp: new Date(messageData.timestamp * 1000).toISOString(),
                footer: {
                    text: `${messageData.message_type === 'whisper_sent' ? 'Sent' : 'Received'} • ${messageData.sender_character}`
                }
            };
            
            const sentMessage = await channel.send({ embeds: [embed] });
            
            Logger.debug(`Sent message to Discord channel ${channelId}`);
            return sentMessage;
            
        } catch (error) {
            Logger.error(`Failed to send message to Discord channel ${channelId}:`, error);
            throw error;
        }
    }
    
    getCharacterAvatar(characterName, realm) {
        // You could implement WoW character avatar fetching here
        // For now, return a default avatar
        return 'https://wow.zamimg.com/images/wow/icons/large/achievement_character_human_male.jpg';
    }
    
    async deleteChannel(channelId) {
        if (!this.isReady) {
            throw new Error('Discord bot is not ready');
        }
        
        try {
            const channel = await this.client.channels.fetch(channelId);
            if (channel) {
                await channel.delete();
                Logger.info(`Deleted Discord channel: ${channelId}`);
            }
        } catch (error) {
            Logger.error(`Failed to delete Discord channel ${channelId}:`, error);
            throw error;
        }
    }
    
    async destroy() {
        if (this.client) {
            await this.client.destroy();
            Logger.info('Discord bot destroyed');
        }
    }
}

module.exports = DiscordBot;
