<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WoW Discord Bridge - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #4CAF50;
        }
        
        .status-offline {
            background-color: #f44336;
        }
        
        .status-warning {
            background-color: #ff9800;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .conversations-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .conversation-item {
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        
        .conversation-participants {
            font-weight: bold;
            color: #333;
        }
        
        .conversation-time {
            font-size: 0.9em;
            color: #666;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.2s;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .error {
            color: #f44336;
            background: #ffebee;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 WoW Discord Bridge</h1>
            <p>Real-time monitoring dashboard</p>
        </div>
        
        <div class="dashboard">
            <!-- System Status Card -->
            <div class="card">
                <h3>🔧 System Status</h3>
                <div id="system-status">
                    <div class="loading">Loading...</div>
                </div>
            </div>
            
            <!-- Statistics Card -->
            <div class="card">
                <h3>📊 Statistics</h3>
                <div id="statistics">
                    <div class="loading">Loading...</div>
                </div>
            </div>
            
            <!-- Active Conversations Card -->
            <div class="card">
                <h3>💬 Active Conversations</h3>
                <div id="conversations">
                    <div class="loading">Loading...</div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Data</button>
        </div>
    </div>

    <script>
        let refreshInterval;
        
        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Error fetching ${endpoint}:`, error);
                throw error;
            }
        }
        
        async function updateSystemStatus() {
            const container = document.getElementById('system-status');
            
            try {
                const data = await fetchData('/api/status');
                
                if (data.success) {
                    const status = data.status;
                    container.innerHTML = `
                        <div class="metric">
                            <span>Server</span>
                            <span><span class="status-indicator status-online"></span>Running</span>
                        </div>
                        <div class="metric">
                            <span>Database</span>
                            <span><span class="status-indicator status-online"></span>Connected</span>
                        </div>
                        <div class="metric">
                            <span>Discord Bot</span>
                            <span><span class="status-indicator ${status.discord === 'connected' ? 'status-online' : 'status-offline'}"></span>${status.discord}</span>
                        </div>
                        <div class="metric">
                            <span>Uptime</span>
                            <span class="metric-value">${formatUptime(status.uptime)}</span>
                        </div>
                        <div class="metric">
                            <span>Memory Usage</span>
                            <span class="metric-value">${formatMemory(status.memory.heapUsed)}</span>
                        </div>
                    `;
                } else {
                    throw new Error('Invalid response format');
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Failed to load system status: ${error.message}</div>`;
            }
        }
        
        async function updateStatistics() {
            const container = document.getElementById('statistics');
            
            try {
                const data = await fetchData('/api/status');
                
                if (data.success && data.status.stats) {
                    const stats = data.status.stats;
                    container.innerHTML = `
                        <div class="metric">
                            <span>Active Conversations</span>
                            <span class="metric-value">${stats.activeConversations}</span>
                        </div>
                        <div class="metric">
                            <span>Messages (24h)</span>
                            <span class="metric-value">${stats.messagesLast24h}</span>
                        </div>
                        <div class="metric">
                            <span>Last Updated</span>
                            <span class="metric-value">${new Date().toLocaleTimeString()}</span>
                        </div>
                    `;
                } else {
                    throw new Error('No statistics available');
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Failed to load statistics: ${error.message}</div>`;
            }
        }
        
        async function updateConversations() {
            const container = document.getElementById('conversations');
            
            try {
                const data = await fetchData('/api/conversations');
                
                if (data.success) {
                    const conversations = data.conversations;
                    
                    if (conversations.length === 0) {
                        container.innerHTML = '<div style="text-align: center; color: #666;">No active conversations</div>';
                        return;
                    }
                    
                    const conversationsHtml = conversations.slice(0, 5).map(conv => `
                        <div class="conversation-item">
                            <div class="conversation-participants">
                                ${conv.participant1_name} ↔ ${conv.participant2_name}
                            </div>
                            <div class="conversation-time">
                                Last activity: ${formatTime(conv.last_activity)}
                            </div>
                        </div>
                    `).join('');
                    
                    container.innerHTML = `
                        <div class="conversations-list">
                            ${conversationsHtml}
                        </div>
                        ${conversations.length > 5 ? `<div style="text-align: center; margin-top: 10px; color: #666;">... and ${conversations.length - 5} more</div>` : ''}
                    `;
                } else {
                    throw new Error('Invalid response format');
                }
            } catch (error) {
                container.innerHTML = `<div class="error">Failed to load conversations: ${error.message}</div>`;
            }
        }
        
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
        
        function formatMemory(bytes) {
            const mb = bytes / 1024 / 1024;
            return `${mb.toFixed(1)} MB`;
        }
        
        function formatTime(timestamp) {
            return new Date(timestamp).toLocaleString();
        }
        
        async function refreshData() {
            await Promise.all([
                updateSystemStatus(),
                updateStatistics(),
                updateConversations()
            ]);
        }
        
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 30000); // Refresh every 30 seconds
        }
        
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            refreshData();
            startAutoRefresh();
        });
        
        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
