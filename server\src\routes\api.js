const express = require('express');
const router = express.Router();
const Logger = require('../utils/logger');

// Rate limiting middleware (simple implementation)
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000; // 1 minute
const RATE_LIMIT_MAX = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100;

function rateLimit(req, res, next) {
    const clientId = req.ip;
    const now = Date.now();
    
    if (!rateLimitMap.has(clientId)) {
        rateLimitMap.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
        return next();
    }
    
    const clientData = rateLimitMap.get(clientId);
    
    if (now > clientData.resetTime) {
        // Reset the rate limit window
        clientData.count = 1;
        clientData.resetTime = now + RATE_LIMIT_WINDOW;
        return next();
    }
    
    if (clientData.count >= RATE_LIMIT_MAX) {
        return res.status(429).json({
            error: 'Rate limit exceeded',
            retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
        });
    }
    
    clientData.count++;
    next();
}

// Apply rate limiting to all API routes
router.use(rateLimit);

// Middleware to ensure required services are available
router.use((req, res, next) => {
    if (!req.app.locals.conversationManager) {
        return res.status(503).json({ error: 'Conversation manager not available' });
    }
    if (!req.app.locals.database) {
        return res.status(503).json({ error: 'Database not available' });
    }
    if (!req.app.locals.discordBot) {
        return res.status(503).json({ error: 'Discord bot not available' });
    }
    next();
});

// POST /api/message - Receive messages from WoW addon
router.post('/message', async (req, res) => {
    try {
        const messageData = req.body;
        
        // Validate required fields
        const requiredFields = [
            'type', 'sender', 'recipient', 'message', 'timestamp', 'conversationId'
        ];
        
        for (const field of requiredFields) {
            if (!messageData[field]) {
                return res.status(400).json({
                    error: `Missing required field: ${field}`
                });
            }
        }
        
        // Validate sender and recipient objects
        const requiredPersonFields = ['name', 'realm', 'character'];
        for (const field of requiredPersonFields) {
            if (!messageData.sender[field] || !messageData.recipient[field]) {
                return res.status(400).json({
                    error: `Missing required person field: ${field}`
                });
            }
        }
        
        // Transform the data to match our internal format
        const transformedData = {
            type: messageData.type,
            sender_name: messageData.sender.name,
            sender_realm: messageData.sender.realm,
            sender_character: messageData.sender.character,
            recipient_name: messageData.recipient.name,
            recipient_realm: messageData.recipient.realm,
            recipient_character: messageData.recipient.character,
            message: messageData.message,
            timestamp: messageData.timestamp,
            conversationId: messageData.conversationId
        };
        
        Logger.debug('Received message from WoW:', transformedData);
        
        // Process the message
        await req.app.locals.conversationManager.handleWoWMessage(transformedData);
        
        res.json({
            success: true,
            message: 'Message processed successfully',
            conversationId: messageData.conversationId
        });
        
    } catch (error) {
        Logger.error('Error processing WoW message:', error);
        res.status(500).json({
            error: 'Failed to process message',
            details: error.message
        });
    }
});

// GET /api/conversations - Get all active conversations
router.get('/conversations', async (req, res) => {
    try {
        const conversations = await req.app.locals.database.all(
            'SELECT * FROM conversations WHERE is_active = 1 ORDER BY last_activity DESC'
        );
        
        res.json({
            success: true,
            conversations: conversations
        });
        
    } catch (error) {
        Logger.error('Error fetching conversations:', error);
        res.status(500).json({
            error: 'Failed to fetch conversations',
            details: error.message
        });
    }
});

// GET /api/conversations/:id - Get specific conversation
router.get('/conversations/:id', async (req, res) => {
    try {
        const conversationId = req.params.id;
        const conversation = await req.app.locals.database.getConversation(conversationId);
        
        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found'
            });
        }
        
        res.json({
            success: true,
            conversation: conversation
        });
        
    } catch (error) {
        Logger.error('Error fetching conversation:', error);
        res.status(500).json({
            error: 'Failed to fetch conversation',
            details: error.message
        });
    }
});

// GET /api/conversations/:id/messages - Get conversation messages
router.get('/conversations/:id/messages', async (req, res) => {
    try {
        const conversationId = req.params.id;
        const limit = parseInt(req.query.limit) || 50;
        
        const messages = await req.app.locals.conversationManager.getConversationHistory(
            conversationId, 
            limit
        );
        
        res.json({
            success: true,
            messages: messages,
            count: messages.length
        });
        
    } catch (error) {
        Logger.error('Error fetching conversation messages:', error);
        res.status(500).json({
            error: 'Failed to fetch messages',
            details: error.message
        });
    }
});

// POST /api/characters - Add/update character
router.post('/characters', async (req, res) => {
    try {
        const { name, realm, isMain } = req.body;
        
        if (!name || !realm) {
            return res.status(400).json({
                error: 'Name and realm are required'
            });
        }
        
        await req.app.locals.database.addCharacter(name, realm, isMain || false);
        
        res.json({
            success: true,
            message: 'Character added/updated successfully'
        });
        
    } catch (error) {
        Logger.error('Error adding character:', error);
        res.status(500).json({
            error: 'Failed to add character',
            details: error.message
        });
    }
});

// GET /api/characters - Get all characters
router.get('/characters', async (req, res) => {
    try {
        const characters = await req.app.locals.database.getCharacters();
        
        res.json({
            success: true,
            characters: characters
        });
        
    } catch (error) {
        Logger.error('Error fetching characters:', error);
        res.status(500).json({
            error: 'Failed to fetch characters',
            details: error.message
        });
    }
});

// POST /api/test - Test endpoint for debugging
router.post('/test', async (req, res) => {
    try {
        const testMessage = {
            type: 'test',
            sender_name: 'TestSender',
            sender_realm: 'TestRealm',
            sender_character: 'TestSender',
            recipient_name: 'TestRecipient',
            recipient_realm: 'TestRealm',
            recipient_character: 'TestRecipient',
            message: 'This is a test message from API',
            timestamp: Math.floor(Date.now() / 1000),
            conversationId: 'test_conversation_' + Date.now()
        };
        
        await req.app.locals.conversationManager.handleWoWMessage(testMessage);
        
        res.json({
            success: true,
            message: 'Test message sent successfully',
            testData: testMessage
        });
        
    } catch (error) {
        Logger.error('Error sending test message:', error);
        res.status(500).json({
            error: 'Failed to send test message',
            details: error.message
        });
    }
});

// GET /api/status - Get system status
router.get('/status', async (req, res) => {
    try {
        const status = {
            server: 'running',
            database: 'connected',
            discord: req.app.locals.discordBot.isReady ? 'connected' : 'disconnected',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            timestamp: new Date().toISOString()
        };
        
        // Get some stats
        const conversationCount = await req.app.locals.database.get(
            'SELECT COUNT(*) as count FROM conversations WHERE is_active = 1'
        );
        
        const messageCount = await req.app.locals.database.get(
            'SELECT COUNT(*) as count FROM messages WHERE created_at > datetime("now", "-24 hours")'
        );
        
        status.stats = {
            activeConversations: conversationCount?.count || 0,
            messagesLast24h: messageCount?.count || 0
        };
        
        res.json({
            success: true,
            status: status
        });
        
    } catch (error) {
        Logger.error('Error getting status:', error);
        res.status(500).json({
            error: 'Failed to get status',
            details: error.message
        });
    }
});

module.exports = router;
