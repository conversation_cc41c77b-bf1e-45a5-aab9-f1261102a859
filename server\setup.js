#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class SetupWizard {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        this.config = {};
    }
    
    async run() {
        console.log('🎮 WoW Discord Bridge Setup Wizard');
        console.log('=====================================\n');
        
        try {
            await this.createDirectories();
            await this.collectConfiguration();
            await this.createEnvFile();
            await this.initializeDatabase();
            
            console.log('\n✅ Setup completed successfully!');
            console.log('\nNext steps:');
            console.log('1. Start the server: npm start');
            console.log('2. Test the connection: /db test in WoW');
            console.log('3. Check the logs in ./logs/ directory');
            
        } catch (error) {
            console.error('\n❌ Setup failed:', error.message);
            process.exit(1);
        } finally {
            this.rl.close();
        }
    }
    
    async createDirectories() {
        console.log('📁 Creating necessary directories...');
        
        const directories = [
            './data',
            './logs',
            './messages',
            './public'
        ];
        
        for (const dir of directories) {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`   Created: ${dir}`);
            } else {
                console.log(`   Exists: ${dir}`);
            }
        }
        
        console.log('✅ Directories created\n');
    }
    
    async collectConfiguration() {
        console.log('⚙️  Configuration Setup');
        console.log('Please provide the following information:\n');
        
        // Discord Bot Token
        this.config.DISCORD_BOT_TOKEN = await this.question(
            '🤖 Discord Bot Token: '
        );
        
        // Discord Guild ID
        this.config.DISCORD_GUILD_ID = await this.question(
            '🏠 Discord Server ID: '
        );
        
        // Discord Category ID (optional)
        const categoryId = await this.question(
            '📁 Discord Category ID (optional, press Enter to skip): '
        );
        if (categoryId.trim()) {
            this.config.DISCORD_CATEGORY_ID = categoryId.trim();
        }
        
        // Server Configuration
        const port = await this.question(
            '🌐 Server Port (default: 3000): '
        );
        this.config.PORT = port.trim() || '3000';
        
        const host = await this.question(
            '🖥️  Server Host (default: localhost): '
        );
        this.config.HOST = host.trim() || 'localhost';
        
        // WoW Configuration
        const wowPath = await this.question(
            '🎮 WoW Account Name (for SavedVariables path): '
        );
        if (wowPath.trim()) {
            this.config.WOW_ADDON_DATA_PATH = `../WTF/Account/${wowPath.trim()}/SavedVariables/DiscordBridge.lua`;
        }
        
        // Debug mode
        const debug = await this.question(
            '🐛 Enable debug mode? (y/N): '
        );
        this.config.DEBUG = debug.toLowerCase().startsWith('y') ? 'true' : 'false';
        
        console.log('\n✅ Configuration collected\n');
    }
    
    async createEnvFile() {
        console.log('📝 Creating .env file...');
        
        const envContent = `# Discord Bot Configuration
DISCORD_BOT_TOKEN=${this.config.DISCORD_BOT_TOKEN}
DISCORD_GUILD_ID=${this.config.DISCORD_GUILD_ID}
${this.config.DISCORD_CATEGORY_ID ? `DISCORD_CATEGORY_ID=${this.config.DISCORD_CATEGORY_ID}` : '# DISCORD_CATEGORY_ID=your_category_id_here'}

# Server Configuration
PORT=${this.config.PORT}
HOST=${this.config.HOST}

# Database Configuration
DATABASE_PATH=./data/conversations.db

# WoW Configuration
${this.config.WOW_ADDON_DATA_PATH ? `WOW_ADDON_DATA_PATH=${this.config.WOW_ADDON_DATA_PATH}` : '# WOW_ADDON_DATA_PATH=../WTF/Account/YOUR_ACCOUNT/SavedVariables/DiscordBridge.lua'}

# Logging
LOG_LEVEL=info
DEBUG=${this.config.DEBUG}

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Channel Management
MAX_CHANNELS_PER_USER=10
CHANNEL_CLEANUP_DAYS=7
CHANNEL_NAME_PREFIX=whisper-
`;
        
        fs.writeFileSync('.env', envContent);
        console.log('✅ .env file created\n');
    }
    
    async initializeDatabase() {
        console.log('🗄️  Initializing database...');
        
        try {
            const Database = require('./src/database/database');
            const db = new Database();
            await db.initialize();
            await db.close();
            
            console.log('✅ Database initialized\n');
        } catch (error) {
            console.log('⚠️  Database initialization will happen on first run\n');
        }
    }
    
    question(prompt) {
        return new Promise((resolve) => {
            this.rl.question(prompt, resolve);
        });
    }
}

// Run setup if called directly
if (require.main === module) {
    const wizard = new SetupWizard();
    wizard.run().catch(console.error);
}

module.exports = SetupWizard;
